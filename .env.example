# Server hostname
# This will be used for generating the downloaded media URLs
HOST=http://localhost

# Port where the server will run
PORT=3000

# Password to protect the API endpoints
# WARNING: This is highly recommended in a production server
AUTH_TOKEN=admin

# URL of the webhooks to receive notifications
# Use the route test-hook from the application itself for testing
WEBHOOK_URL=http://localhost:3000/test-hook

# Webhook secret token
# It will be sent as a Bearer token in the Authorization header of the request
WEBHOOK_SECRET=admin

# Path of the Chrome browser binary (if you need to customize it)
# This is used on Puppeteer emulation only
CHROME_PATH=

# Path of the ffmpeg binary for sending videos
# This is used on Puppeteer emulation only
FFMPEG_PATH=